// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "realtime-speech-recognition",
    platforms: [
        .macOS(.v14)
    ],
    dependencies: [
        .package(url: "https://github.com/argmaxinc/WhisperKit.git", from: "0.13.1")
    ],
    targets: [
        .target(
            name: "realtime-speech-recognition",
            dependencies: ["WhisperKit"]
        )
    ]
)
