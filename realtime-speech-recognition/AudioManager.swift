//
//  AudioManager.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import Foundation
import AVFoundation
import Combine

@MainActor
class AudioManager: NSObject, ObservableObject {
    @Published var isRecording = false
    @Published var hasPermission = false
    @Published var permissionStatus: AVAudioSession.RecordPermission = .undetermined
    @Published var audioLevel: Float = 0.0
    
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var audioFormat: AVAudioFormat?
    
    // 音频数据回调
    var audioDataCallback: ((Data) -> Void)?
    
    override init() {
        super.init()
        setupAudioSession()
        checkPermissions()
    }
    
    // MARK: - 权限管理
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: [])
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    func checkPermissions() {
        permissionStatus = AVAudioSession.sharedInstance().recordPermission
        hasPermission = permissionStatus == .granted
    }
    
    func requestPermission() async {
        let granted = await AVAudioSession.sharedInstance().requestRecordPermission()
        await MainActor.run {
            hasPermission = granted
            permissionStatus = granted ? .granted : .denied
        }
    }
    
    // MARK: - 录制控制
    
    func startRecording() async throws {
        guard hasPermission else {
            await requestPermission()
            guard hasPermission else {
                throw AudioError.permissionDenied
            }
        }
        
        try setupAudioEngine()
        
        do {
            try audioEngine?.start()
            isRecording = true
            print("Audio recording started")
        } catch {
            print("Failed to start audio engine: \(error)")
            throw AudioError.engineStartFailed
        }
    }
    
    func stopRecording() {
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        isRecording = false
        print("Audio recording stopped")
    }
    
    // MARK: - 音频引擎设置
    
    private func setupAudioEngine() throws {
        audioEngine = AVAudioEngine()
        
        guard let audioEngine = audioEngine else {
            throw AudioError.engineSetupFailed
        }
        
        inputNode = audioEngine.inputNode
        
        // 设置音频格式 - 16kHz, 单声道, 16位
        // WhisperKit推荐的格式
        audioFormat = AVAudioFormat(
            commonFormat: .pcmFormatFloat32,
            sampleRate: 16000,
            channels: 1,
            interleaved: false
        )
        
        guard let audioFormat = audioFormat else {
            throw AudioError.formatSetupFailed
        }
        
        // 安装音频tap来捕获音频数据
        inputNode?.installTap(onBus: 0, bufferSize: 1024, format: audioFormat) { [weak self] buffer, time in
            self?.processAudioBuffer(buffer)
        }
    }
    
    // MARK: - 音频数据处理
    
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData?[0] else { return }
        
        let frameLength = Int(buffer.frameLength)
        let audioData = Data(bytes: channelData, count: frameLength * MemoryLayout<Float>.size)
        
        // 计算音频电平（用于UI显示）
        let rms = calculateRMS(channelData: channelData, frameLength: frameLength)
        
        Task { @MainActor in
            self.audioLevel = rms
        }
        
        // 回调音频数据给语音识别模块
        audioDataCallback?(audioData)
    }
    
    private func calculateRMS(channelData: UnsafePointer<Float>, frameLength: Int) -> Float {
        var sum: Float = 0
        for i in 0..<frameLength {
            sum += channelData[i] * channelData[i]
        }
        return sqrt(sum / Float(frameLength))
    }
}

// MARK: - 错误定义

enum AudioError: Error, LocalizedError {
    case permissionDenied
    case engineSetupFailed
    case engineStartFailed
    case formatSetupFailed
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "麦克风权限被拒绝"
        case .engineSetupFailed:
            return "音频引擎设置失败"
        case .engineStartFailed:
            return "音频引擎启动失败"
        case .formatSetupFailed:
            return "音频格式设置失败"
        }
    }
}
