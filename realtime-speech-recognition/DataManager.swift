//
//  DataManager.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import Foundation
import SwiftUI

// 转录会话数据模型
struct TranscriptionSession: Identifiable, Codable {
    let id = UUID()
    let title: String
    let startTime: Date
    let endTime: Date
    let segments: [TranscriptionSegment]
    let totalWords: Int
    let averageConfidence: Float
    
    var duration: TimeInterval {
        return endTime.timeIntervalSince(startTime)
    }
    
    var formattedDuration: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration) ?? "0s"
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: startTime)
    }
    
    var fullText: String {
        return segments.map { $0.text }.joined(separator: " ")
    }
}

@MainActor
class DataManager: ObservableObject {
    @Published var sessions: [TranscriptionSession] = []
    @Published var currentSession: TranscriptionSession?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let documentsDirectory: URL
    private let sessionsFileName = "transcription_sessions.json"
    
    init() {
        documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        loadSessions()
    }
    
    // MARK: - 会话管理
    
    func startNewSession(title: String? = nil) {
        let sessionTitle = title ?? "转录会话 \(DateFormatter.sessionFormatter.string(from: Date()))"
        
        currentSession = TranscriptionSession(
            title: sessionTitle,
            startTime: Date(),
            endTime: Date(),
            segments: [],
            totalWords: 0,
            averageConfidence: 0.0
        )
    }
    
    func endCurrentSession(segments: [TranscriptionSegment]) {
        guard let current = currentSession else { return }
        
        let totalWords = segments.reduce(0) { $0 + $1.words.count }
        let allWords = segments.flatMap { $0.words }
        let averageConfidence = allWords.isEmpty ? 0.0 : allWords.reduce(0) { $0 + $1.confidence } / Float(allWords.count)
        
        let completedSession = TranscriptionSession(
            title: current.title,
            startTime: current.startTime,
            endTime: Date(),
            segments: segments,
            totalWords: totalWords,
            averageConfidence: averageConfidence
        )
        
        sessions.insert(completedSession, at: 0) // 最新的在前面
        currentSession = nil
        
        saveSessions()
    }
    
    func deleteSession(_ session: TranscriptionSession) {
        sessions.removeAll { $0.id == session.id }
        saveSessions()
    }
    
    func deleteAllSessions() {
        sessions.removeAll()
        saveSessions()
    }
    
    // MARK: - 数据持久化
    
    private func loadSessions() {
        isLoading = true
        
        let url = documentsDirectory.appendingPathComponent(sessionsFileName)
        
        do {
            let data = try Data(contentsOf: url)
            sessions = try JSONDecoder().decode([TranscriptionSession].self, from: data)
            print("加载了 \(sessions.count) 个转录会话")
        } catch {
            if !(error is CocoaError && (error as! CocoaError).code == .fileReadNoSuchFile) {
                print("加载会话失败: \(error)")
                errorMessage = "加载历史记录失败: \(error.localizedDescription)"
            }
        }
        
        isLoading = false
    }
    
    private func saveSessions() {
        let url = documentsDirectory.appendingPathComponent(sessionsFileName)
        
        do {
            let data = try JSONEncoder().encode(sessions)
            try data.write(to: url)
            print("保存了 \(sessions.count) 个转录会话")
        } catch {
            print("保存会话失败: \(error)")
            errorMessage = "保存失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 导出功能
    
    func exportSession(_ session: TranscriptionSession, format: ExportFormat) -> String {
        switch format {
        case .plainText:
            return session.fullText
            
        case .withTimestamps:
            return session.segments.map { segment in
                let timeStr = segment.formattedTime
                return "[\(timeStr)] \(segment.text)"
            }.joined(separator: "\n")
            
        case .detailed:
            var result = """
            转录会话: \(session.title)
            开始时间: \(session.formattedDate)
            持续时间: \(session.formattedDuration)
            总词数: \(session.totalWords)
            平均置信度: \(Int(session.averageConfidence * 100))%
            
            转录内容:
            ========
            
            """
            
            result += session.segments.enumerated().map { index, segment in
                let timeStr = segment.formattedTime
                return "\(index + 1). [\(timeStr)] \(segment.text)"
            }.joined(separator: "\n")
            
            return result
            
        case .json:
            do {
                let data = try JSONEncoder().encode(session)
                return String(data: data, encoding: .utf8) ?? "JSON编码失败"
            } catch {
                return "JSON导出失败: \(error.localizedDescription)"
            }
        }
    }
    
    func exportAllSessions(format: ExportFormat) -> String {
        switch format {
        case .plainText:
            return sessions.map { $0.fullText }.joined(separator: "\n\n")
            
        case .withTimestamps, .detailed:
            return sessions.map { exportSession($0, format: format) }.joined(separator: "\n\n" + String(repeating: "=", count: 50) + "\n\n")
            
        case .json:
            do {
                let data = try JSONEncoder().encode(sessions)
                return String(data: data, encoding: .utf8) ?? "JSON编码失败"
            } catch {
                return "JSON导出失败: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - 搜索功能
    
    func searchSessions(query: String) -> [TranscriptionSession] {
        guard !query.isEmpty else { return sessions }
        
        return sessions.filter { session in
            session.title.localizedCaseInsensitiveContains(query) ||
            session.fullText.localizedCaseInsensitiveContains(query)
        }
    }
    
    // MARK: - 统计信息
    
    var totalSessions: Int {
        return sessions.count
    }
    
    var totalWords: Int {
        return sessions.reduce(0) { $0 + $1.totalWords }
    }
    
    var totalDuration: TimeInterval {
        return sessions.reduce(0) { $0 + $1.duration }
    }
    
    var formattedTotalDuration: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .full
        return formatter.string(from: totalDuration) ?? "0秒"
    }
}

// 导出格式枚举
enum ExportFormat: String, CaseIterable {
    case plainText = "纯文本"
    case withTimestamps = "带时间戳"
    case detailed = "详细信息"
    case json = "JSON格式"
}

extension DateFormatter {
    static let sessionFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter
    }()
}
