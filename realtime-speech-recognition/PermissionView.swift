//
//  PermissionView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI
import AVFoundation

struct PermissionView: View {
    @ObservedObject var audioManager: AudioManager
    @State private var isRequestingPermission = false
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "mic.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.blue)
            
            Text("需要麦克风权限")
                .font(.title)
                .fontWeight(.semibold)
            
            Text("此应用需要访问您的麦克风来进行实时语音识别。")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal)
            
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("实时语音转文字")
                }
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("本地处理，保护隐私")
                }
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("支持连续识别")
                }
            }
            .font(.subheadline)
            
            Button(action: requestPermission) {
                HStack {
                    if isRequestingPermission {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "mic")
                    }
                    Text(isRequestingPermission ? "请求中..." : "允许麦克风访问")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            .disabled(isRequestingPermission)
            .padding(.horizontal)
            
            if audioManager.permissionStatus == .denied {
                VStack(spacing: 8) {
                    Text("权限被拒绝")
                        .font(.headline)
                        .foregroundColor(.red)
                    
                    Text("请在系统设置中手动开启麦克风权限")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Button("打开系统设置") {
                        openSystemPreferences()
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(8)
                .padding(.horizontal)
            }
        }
        .padding()
        .frame(maxWidth: 400)
    }
    
    private func requestPermission() {
        isRequestingPermission = true
        
        Task {
            await audioManager.requestPermission()
            await MainActor.run {
                isRequestingPermission = false
            }
        }
    }
    
    private func openSystemPreferences() {
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone") {
            NSWorkspace.shared.open(url)
        }
    }
}

#Preview {
    PermissionView(audioManager: AudioManager())
}
