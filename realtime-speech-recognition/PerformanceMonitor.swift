//
//  PerformanceMonitor.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import Foundation
import SwiftUI

// 性能指标
struct PerformanceMetrics {
    let cpuUsage: Double
    let memoryUsage: Double
    let transcriptionLatency: TimeInterval
    let audioBufferHealth: Double
    let modelResponseTime: TimeInterval
    let timestamp: Date
}

@MainActor
class PerformanceMonitor: ObservableObject {
    @Published var currentMetrics: PerformanceMetrics?
    @Published var metricsHistory: [PerformanceMetrics] = []
    @Published var isMonitoring = false
    @Published var performanceAlerts: [PerformanceAlert] = []
    
    private var monitoringTimer: Timer?
    private let maxHistorySize = 100
    private var lastTranscriptionStart: Date?
    
    // 性能阈值
    private let cpuThreshold: Double = 80.0
    private let memoryThreshold: Double = 500.0 // MB
    private let latencyThreshold: TimeInterval = 2.0 // 秒
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            Task { @MainActor in
                self.collectMetrics()
            }
        }
        
        print("性能监控已启动")
    }
    
    func stopMonitoring() {
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        print("性能监控已停止")
    }
    
    func recordTranscriptionStart() {
        lastTranscriptionStart = Date()
    }
    
    func recordTranscriptionEnd() {
        guard let startTime = lastTranscriptionStart else { return }
        let latency = Date().timeIntervalSince(startTime)
        
        // 检查延迟是否超过阈值
        if latency > latencyThreshold {
            addAlert(.highLatency(latency))
        }
        
        lastTranscriptionStart = nil
    }
    
    private func collectMetrics() {
        let metrics = PerformanceMetrics(
            cpuUsage: getCPUUsage(),
            memoryUsage: getMemoryUsage(),
            transcriptionLatency: lastTranscriptionStart?.timeIntervalSinceNow ?? 0,
            audioBufferHealth: getAudioBufferHealth(),
            modelResponseTime: getModelResponseTime(),
            timestamp: Date()
        )
        
        currentMetrics = metrics
        metricsHistory.append(metrics)
        
        // 限制历史记录大小
        if metricsHistory.count > maxHistorySize {
            metricsHistory.removeFirst(metricsHistory.count - maxHistorySize)
        }
        
        // 检查性能警告
        checkPerformanceThresholds(metrics)
    }
    
    private func getCPUUsage() -> Double {
        // 简化的CPU使用率计算
        // 在实际应用中，可以使用更精确的系统API
        return Double.random(in: 10...30) // 模拟数据
    }
    
    private func getMemoryUsage() -> Double {
        let info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0 // MB
        } else {
            return 0.0
        }
    }
    
    private func getAudioBufferHealth() -> Double {
        // 音频缓冲区健康度（0-1）
        // 在实际应用中，这应该从AudioManager获取
        return Double.random(in: 0.8...1.0)
    }
    
    private func getModelResponseTime() -> TimeInterval {
        // 模型响应时间
        // 在实际应用中，这应该从SpeechRecognitionManager获取
        return Double.random(in: 0.1...0.5)
    }
    
    private func checkPerformanceThresholds(_ metrics: PerformanceMetrics) {
        // CPU使用率检查
        if metrics.cpuUsage > cpuThreshold {
            addAlert(.highCPU(metrics.cpuUsage))
        }
        
        // 内存使用检查
        if metrics.memoryUsage > memoryThreshold {
            addAlert(.highMemory(metrics.memoryUsage))
        }
        
        // 音频缓冲区健康度检查
        if metrics.audioBufferHealth < 0.5 {
            addAlert(.audioBufferIssue(metrics.audioBufferHealth))
        }
    }
    
    private func addAlert(_ alert: PerformanceAlert) {
        // 避免重复警告
        if !performanceAlerts.contains(where: { $0.type == alert.type }) {
            performanceAlerts.append(alert)
            
            // 限制警告数量
            if performanceAlerts.count > 10 {
                performanceAlerts.removeFirst()
            }
            
            print("性能警告: \(alert.message)")
        }
    }
    
    func dismissAlert(_ alert: PerformanceAlert) {
        performanceAlerts.removeAll { $0.id == alert.id }
    }
    
    func clearAllAlerts() {
        performanceAlerts.removeAll()
    }
    
    // 获取性能统计
    func getPerformanceStatistics() -> PerformanceStatistics {
        guard !metricsHistory.isEmpty else {
            return PerformanceStatistics(
                averageCPU: 0,
                averageMemory: 0,
                averageLatency: 0,
                maxCPU: 0,
                maxMemory: 0,
                maxLatency: 0
            )
        }
        
        let cpuValues = metricsHistory.map { $0.cpuUsage }
        let memoryValues = metricsHistory.map { $0.memoryUsage }
        let latencyValues = metricsHistory.map { $0.transcriptionLatency }
        
        return PerformanceStatistics(
            averageCPU: cpuValues.reduce(0, +) / Double(cpuValues.count),
            averageMemory: memoryValues.reduce(0, +) / Double(memoryValues.count),
            averageLatency: latencyValues.reduce(0, +) / Double(latencyValues.count),
            maxCPU: cpuValues.max() ?? 0,
            maxMemory: memoryValues.max() ?? 0,
            maxLatency: latencyValues.max() ?? 0
        )
    }
}

// 性能警告
struct PerformanceAlert: Identifiable {
    let id = UUID()
    let type: AlertType
    let message: String
    let timestamp: Date
    let severity: AlertSeverity
    
    enum AlertType: Equatable {
        case highCPU
        case highMemory
        case highLatency
        case audioBufferIssue
    }
    
    enum AlertSeverity {
        case warning, critical
        
        var color: Color {
            switch self {
            case .warning:
                return .orange
            case .critical:
                return .red
            }
        }
    }
    
    static func highCPU(_ usage: Double) -> PerformanceAlert {
        return PerformanceAlert(
            type: .highCPU,
            message: "CPU使用率过高: \(Int(usage))%",
            timestamp: Date(),
            severity: usage > 90 ? .critical : .warning
        )
    }
    
    static func highMemory(_ usage: Double) -> PerformanceAlert {
        return PerformanceAlert(
            type: .highMemory,
            message: "内存使用过高: \(Int(usage))MB",
            timestamp: Date(),
            severity: usage > 1000 ? .critical : .warning
        )
    }
    
    static func highLatency(_ latency: TimeInterval) -> PerformanceAlert {
        return PerformanceAlert(
            type: .highLatency,
            message: "转录延迟过高: \(String(format: "%.1f", latency))秒",
            timestamp: Date(),
            severity: latency > 5 ? .critical : .warning
        )
    }
    
    static func audioBufferIssue(_ health: Double) -> PerformanceAlert {
        return PerformanceAlert(
            type: .audioBufferIssue,
            message: "音频缓冲区异常: \(Int(health * 100))%",
            timestamp: Date(),
            severity: health < 0.3 ? .critical : .warning
        )
    }
}

// 性能统计
struct PerformanceStatistics {
    let averageCPU: Double
    let averageMemory: Double
    let averageLatency: TimeInterval
    let maxCPU: Double
    let maxMemory: Double
    let maxLatency: TimeInterval
}

// 性能监控视图
struct PerformanceMonitorView: View {
    @ObservedObject var monitor: PerformanceMonitor
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("性能监控")
                .font(.headline)
            
            if let metrics = monitor.currentMetrics {
                VStack(spacing: 8) {
                    MetricRow(title: "CPU使用率", value: "\(Int(metrics.cpuUsage))%", color: metrics.cpuUsage > 70 ? .red : .green)
                    MetricRow(title: "内存使用", value: "\(Int(metrics.memoryUsage))MB", color: metrics.memoryUsage > 400 ? .red : .green)
                    MetricRow(title: "音频缓冲", value: "\(Int(metrics.audioBufferHealth * 100))%", color: metrics.audioBufferHealth < 0.7 ? .red : .green)
                }
            } else {
                Text("暂无性能数据")
                    .foregroundColor(.secondary)
            }
            
            // 性能警告
            if !monitor.performanceAlerts.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("性能警告")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(monitor.performanceAlerts) { alert in
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(alert.severity.color)
                            Text(alert.message)
                                .font(.caption)
                            Spacer()
                            Button("×") {
                                monitor.dismissAlert(alert)
                            }
                            .font(.caption)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

struct MetricRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
            Spacer()
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}
