//
//  realtime_speech_recognitionApp.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

@main
struct realtime_speech_recognitionApp: App {
    @StateObject private var dataManager = DataManager()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(dataManager)
        }

        WindowGroup("转录历史", id: "history") {
            HistoryView(dataManager: dataManager)
        }
        .commandsRemoved()
    }
}
