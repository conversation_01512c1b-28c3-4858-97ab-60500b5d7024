//
//  VoiceActivityDetector.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import Foundation
import Accelerate

class VoiceActivityDetector {
    // VAD参数
    private let energyThreshold: Float = 0.01
    private let zeroCrossingThreshold: Float = 0.3
    private let minSpeechDuration: TimeInterval = 0.5 // 最小语音持续时间
    private let maxSilenceDuration: TimeInterval = 1.0 // 最大静音持续时间
    
    // 状态跟踪
    private var isSpeechActive = false
    private var speechStartTime: TimeInterval = 0
    private var lastSpeechTime: TimeInterval = 0
    private var silenceStartTime: TimeInterval = 0
    
    // 音频分析缓冲区
    private var energyHistory: [Float] = []
    private var zeroCrossingHistory: [Float] = []
    private let historySize = 10
    
    // 回调
    var onSpeechStart: (() -> Void)?
    var onSpeechEnd: (() -> Void)?
    var onSpeechActivity: ((Bool) -> Void)?
    
    func processAudioBuffer(_ buffer: [Float], sampleRate: Float = 16000.0) -> Bool {
        let currentTime = Date().timeIntervalSince1970
        
        // 计算音频特征
        let energy = calculateEnergy(buffer)
        let zeroCrossingRate = calculateZeroCrossingRate(buffer)
        
        // 更新历史记录
        updateHistory(energy: energy, zeroCrossing: zeroCrossingRate)
        
        // 计算平滑后的特征
        let smoothedEnergy = energyHistory.reduce(0, +) / Float(energyHistory.count)
        let smoothedZCR = zeroCrossingHistory.reduce(0, +) / Float(zeroCrossingHistory.count)
        
        // 语音活动检测
        let hasVoiceActivity = detectVoiceActivity(
            energy: smoothedEnergy,
            zeroCrossingRate: smoothedZCR
        )
        
        // 状态机处理
        processVoiceActivityState(hasVoiceActivity, currentTime: currentTime)
        
        return isSpeechActive
    }
    
    private func calculateEnergy(_ buffer: [Float]) -> Float {
        var energy: Float = 0
        vDSP_svesq(buffer, 1, &energy, vDSP_Length(buffer.count))
        return energy / Float(buffer.count)
    }
    
    private func calculateZeroCrossingRate(_ buffer: [Float]) -> Float {
        var crossings = 0
        for i in 1..<buffer.count {
            if (buffer[i] >= 0) != (buffer[i-1] >= 0) {
                crossings += 1
            }
        }
        return Float(crossings) / Float(buffer.count - 1)
    }
    
    private func updateHistory(energy: Float, zeroCrossing: Float) {
        energyHistory.append(energy)
        zeroCrossingHistory.append(zeroCrossing)
        
        if energyHistory.count > historySize {
            energyHistory.removeFirst()
        }
        
        if zeroCrossingHistory.count > historySize {
            zeroCrossingHistory.removeFirst()
        }
    }
    
    private func detectVoiceActivity(energy: Float, zeroCrossingRate: Float) -> Bool {
        // 基于能量和过零率的简单VAD
        let energyCondition = energy > energyThreshold
        let zcrCondition = zeroCrossingRate < zeroCrossingThreshold
        
        return energyCondition && zcrCondition
    }
    
    private func processVoiceActivityState(_ hasActivity: Bool, currentTime: TimeInterval) {
        if hasActivity {
            lastSpeechTime = currentTime
            
            if !isSpeechActive {
                // 开始检测到语音
                if speechStartTime == 0 {
                    speechStartTime = currentTime
                } else if currentTime - speechStartTime >= minSpeechDuration {
                    // 语音持续时间足够长，确认为语音开始
                    isSpeechActive = true
                    onSpeechStart?()
                    onSpeechActivity?(true)
                    print("语音活动开始")
                }
            }
        } else {
            // 没有检测到语音活动
            if isSpeechActive {
                if silenceStartTime == 0 {
                    silenceStartTime = currentTime
                } else if currentTime - silenceStartTime >= maxSilenceDuration {
                    // 静音持续时间足够长，确认语音结束
                    isSpeechActive = false
                    speechStartTime = 0
                    silenceStartTime = 0
                    onSpeechEnd?()
                    onSpeechActivity?(false)
                    print("语音活动结束")
                }
            } else {
                // 重置语音开始时间
                speechStartTime = 0
                silenceStartTime = 0
            }
        }
    }
    
    // 重置检测器状态
    func reset() {
        isSpeechActive = false
        speechStartTime = 0
        lastSpeechTime = 0
        silenceStartTime = 0
        energyHistory.removeAll()
        zeroCrossingHistory.removeAll()
    }
    
    // 获取当前语音活动状态
    func isCurrentlySpeaking() -> Bool {
        return isSpeechActive
    }
    
    // 调整检测敏感度
    func adjustSensitivity(energyThreshold: Float? = nil, zcrThreshold: Float? = nil) {
        // 这里可以动态调整阈值，但需要重新实现为可变属性
        print("调整VAD敏感度: energy=\(energyThreshold ?? self.energyThreshold), zcr=\(zcrThreshold ?? self.zeroCrossingThreshold)")
    }
}
