//
//  SpeechRecognitionManager.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import Foundation
import WhisperKit
import Combine

@MainActor
class SpeechRecognitionManager: ObservableObject {
    @Published var transcribedText: String = ""
    @Published var isTranscribing = false
    @Published var isModelLoaded = false
    @Published var modelLoadingProgress: Double = 0.0
    @Published var currentSegment: String = ""
    @Published var errorMessage: String?
    @Published var isSpeechActive = false

    private var whisperKit: WhisperKit?
    private var audioBuffer: [Float] = []
    private let bufferSize = 16000 * 30 // 30秒的音频缓冲区
    private let segmentLength = 16000 * 3 // 3秒分段处理（更快响应）
    private let minSegmentLength = 16000 * 1 // 最小1秒分段

    // 语音活动检测
    private let voiceActivityDetector = VoiceActivityDetector()
    private var speechBuffer: [Float] = []
    private var lastTranscriptionTime: Date = Date()

    // 配置参数
    private let modelName = "openai_whisper-base.en" // 英文基础模型
    private let language = "en"
    
    init() {
        loadModel()
        setupVoiceActivityDetector()
    }

    private func setupVoiceActivityDetector() {
        voiceActivityDetector.onSpeechStart = { [weak self] in
            Task { @MainActor in
                self?.isSpeechActive = true
                print("检测到语音开始")
            }
        }

        voiceActivityDetector.onSpeechEnd = { [weak self] in
            Task { @MainActor in
                self?.isSpeechActive = false
                print("检测到语音结束")
                // 语音结束时处理剩余的音频
                self?.processRemainingAudio()
            }
        }
    }
    
    // MARK: - 模型加载
    
    func loadModel() {
        Task {
            do {
                isModelLoaded = false
                modelLoadingProgress = 0.0
                
                print("开始加载WhisperKit模型...")
                
                // 创建WhisperKit配置
                let config = WhisperKitConfig(
                    model: modelName,
                    verbose: true,
                    logLevel: .debug,
                    prewarm: true,
                    load: true,
                    download: true
                )
                
                // 初始化WhisperKit
                whisperKit = try await WhisperKit(config)
                
                isModelLoaded = true
                modelLoadingProgress = 1.0
                print("WhisperKit模型加载成功")
                
            } catch {
                print("WhisperKit模型加载失败: \(error)")
                errorMessage = "模型加载失败: \(error.localizedDescription)"
                isModelLoaded = false
            }
        }
    }
    
    // MARK: - 音频处理

    func processAudioData(_ audioData: Data) {
        guard isModelLoaded else { return }

        // 将音频数据转换为Float数组
        let floatArray = audioData.withUnsafeBytes { bytes in
            Array(bytes.bindMemory(to: Float.self))
        }

        // 语音活动检测
        let hasVoiceActivity = voiceActivityDetector.processAudioBuffer(floatArray)

        // 只有在检测到语音活动时才处理音频
        if hasVoiceActivity {
            speechBuffer.append(contentsOf: floatArray)

            // 检查是否需要进行转录
            let currentTime = Date()
            let timeSinceLastTranscription = currentTime.timeIntervalSince(lastTranscriptionTime)

            // 如果语音缓冲区足够大或者距离上次转录时间足够长，进行转录
            if speechBuffer.count >= segmentLength ||
               (speechBuffer.count >= minSegmentLength && timeSinceLastTranscription >= 2.0) {

                let segmentData = Array(speechBuffer)
                speechBuffer.removeAll() // 清空语音缓冲区
                lastTranscriptionTime = currentTime

                // 异步处理转录
                Task {
                    await transcribeSegment(segmentData)
                }
            }
        }

        // 添加到总缓冲区（用于备份）
        audioBuffer.append(contentsOf: floatArray)

        // 限制缓冲区大小
        if audioBuffer.count > bufferSize {
            audioBuffer.removeFirst(audioBuffer.count - bufferSize)
        }

        // 限制语音缓冲区大小
        if speechBuffer.count > segmentLength * 2 {
            speechBuffer.removeFirst(speechBuffer.count - segmentLength)
        }
    }

    private func processRemainingAudio() {
        // 处理语音结束时剩余的音频
        if !speechBuffer.isEmpty && speechBuffer.count >= minSegmentLength {
            let segmentData = Array(speechBuffer)
            speechBuffer.removeAll()

            Task {
                await transcribeSegment(segmentData)
            }
        }
    }
    
    private func transcribeSegment(_ audioData: [Float]) async {
        guard let whisperKit = whisperKit else { return }
        
        do {
            isTranscribing = true
            
            // 执行转录
            let result = try await whisperKit.transcribe(
                audioArray: audioData,
                decodeOptions: DecodingOptions(
                    language: language,
                    task: .transcribe,
                    temperature: 0.0,
                    temperatureFallbackCount: 3,
                    sampleLength: audioData.count,
                    usePrefillPrompt: true,
                    usePrefillCache: true,
                    skipSpecialTokens: true,
                    withoutTimestamps: false,
                    wordTimestamps: true,
                    clipTimestamps: [0, Double(audioData.count) / 16000.0]
                )
            )
            
            // 处理转录结果
            if let segments = result?.segments, !segments.isEmpty {
                let newText = segments.compactMap { $0.text }.joined(separator: " ")
                
                if !newText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    await MainActor.run {
                        // 更新当前分段
                        currentSegment = newText
                        
                        // 添加到完整转录文本
                        if !transcribedText.isEmpty {
                            transcribedText += " " + newText
                        } else {
                            transcribedText = newText
                        }
                        
                        print("转录结果: \(newText)")
                    }
                }
            }
            
            isTranscribing = false
            
        } catch {
            print("转录失败: \(error)")
            await MainActor.run {
                errorMessage = "转录失败: \(error.localizedDescription)"
                isTranscribing = false
            }
        }
    }
    
    // MARK: - 控制方法
    
    func startTranscription() {
        guard isModelLoaded else {
            errorMessage = "模型未加载完成"
            return
        }

        // 清空之前的结果
        transcribedText = ""
        currentSegment = ""
        audioBuffer.removeAll()
        speechBuffer.removeAll()
        errorMessage = nil
        isSpeechActive = false
        lastTranscriptionTime = Date()

        // 重置语音活动检测器
        voiceActivityDetector.reset()

        print("开始语音转录")
    }

    func stopTranscription() {
        // 处理剩余的语音缓冲区
        processRemainingAudio()

        // 重置状态
        voiceActivityDetector.reset()
        isSpeechActive = false

        print("停止语音转录")
    }
    
    func clearTranscription() {
        transcribedText = ""
        currentSegment = ""
        audioBuffer.removeAll()
        speechBuffer.removeAll()
        errorMessage = nil
        isSpeechActive = false
        voiceActivityDetector.reset()
    }
    
    // MARK: - 实用方法
    
    func exportTranscription() -> String {
        return transcribedText
    }
    
    func getWordCount() -> Int {
        return transcribedText.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }.count
    }
}
