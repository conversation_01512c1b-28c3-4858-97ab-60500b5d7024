//
//  ModelLoadingView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

struct ModelLoadingView: View {
    @ObservedObject var speechManager: SpeechRecognitionManager
    
    var body: some View {
        VStack(spacing: 20) {
            // 加载图标
            ZStack {
                Circle()
                    .stroke(Color.blue.opacity(0.3), lineWidth: 8)
                    .frame(width: 80, height: 80)
                
                Circle()
                    .trim(from: 0, to: speechManager.modelLoadingProgress)
                    .stroke(Color.blue, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: speechManager.modelLoadingProgress)
                
                Image(systemName: "brain")
                    .font(.title)
                    .foregroundColor(.blue)
            }
            
            Text("正在加载语音识别模型")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("首次使用需要下载模型文件，请稍候...")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // 进度信息
            VStack(spacing: 8) {
                ProgressView(value: speechManager.modelLoadingProgress, total: 1.0)
                    .frame(width: 300)
                
                Text("\(Int(speechManager.modelLoadingProgress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 加载状态说明
            VStack(spacing: 4) {
                HStack {
                    Image(systemName: speechManager.modelLoadingProgress > 0.3 ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(speechManager.modelLoadingProgress > 0.3 ? .green : .gray)
                    Text("下载模型文件")
                        .font(.caption)
                }
                
                HStack {
                    Image(systemName: speechManager.modelLoadingProgress > 0.7 ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(speechManager.modelLoadingProgress > 0.7 ? .green : .gray)
                    Text("初始化模型")
                        .font(.caption)
                }
                
                HStack {
                    Image(systemName: speechManager.isModelLoaded ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(speechManager.isModelLoaded ? .green : .gray)
                    Text("准备就绪")
                        .font(.caption)
                }
            }
            .foregroundColor(.secondary)
            
            if let errorMessage = speechManager.errorMessage {
                VStack(spacing: 8) {
                    Text("加载失败")
                        .font(.headline)
                        .foregroundColor(.red)
                    
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Button("重试") {
                        speechManager.loadModel()
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .frame(maxWidth: 400)
    }
}

#Preview {
    ModelLoadingView(speechManager: SpeechRecognitionManager())
}
