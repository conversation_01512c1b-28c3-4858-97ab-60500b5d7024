//
//  ErrorHandler.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import Foundation
import SwiftUI

// 应用错误类型
enum AppError: Error, LocalizedError {
    case audioPermissionDenied
    case audioEngineFailure(String)
    case modelLoadingFailed(String)
    case transcriptionFailed(String)
    case dataStorageError(String)
    case networkError(String)
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .audioPermissionDenied:
            return "麦克风权限被拒绝"
        case .audioEngineFailure(let message):
            return "音频引擎错误: \(message)"
        case .modelLoadingFailed(let message):
            return "模型加载失败: \(message)"
        case .transcriptionFailed(let message):
            return "语音识别失败: \(message)"
        case .dataStorageError(let message):
            return "数据存储错误: \(message)"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .unknownError(let message):
            return "未知错误: \(message)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .audioPermissionDenied:
            return "请在系统设置中允许应用访问麦克风"
        case .audioEngineFailure:
            return "请检查音频设备连接，然后重试"
        case .modelLoadingFailed:
            return "请检查网络连接，或重启应用重新下载模型"
        case .transcriptionFailed:
            return "请确保说话清晰，环境安静，然后重试"
        case .dataStorageError:
            return "请检查磁盘空间，确保应用有写入权限"
        case .networkError:
            return "请检查网络连接，然后重试"
        case .unknownError:
            return "请重启应用，如果问题持续存在，请联系技术支持"
        }
    }
    
    var severity: ErrorSeverity {
        switch self {
        case .audioPermissionDenied, .modelLoadingFailed:
            return .critical
        case .audioEngineFailure, .transcriptionFailed:
            return .high
        case .dataStorageError, .networkError:
            return .medium
        case .unknownError:
            return .low
        }
    }
}

enum ErrorSeverity {
    case critical, high, medium, low
    
    var color: Color {
        switch self {
        case .critical:
            return .red
        case .high:
            return .orange
        case .medium:
            return .yellow
        case .low:
            return .blue
        }
    }
    
    var icon: String {
        switch self {
        case .critical:
            return "exclamationmark.triangle.fill"
        case .high:
            return "exclamationmark.circle.fill"
        case .medium:
            return "info.circle.fill"
        case .low:
            return "questionmark.circle.fill"
        }
    }
}

@MainActor
class ErrorHandler: ObservableObject {
    @Published var currentError: AppError?
    @Published var showingError = false
    @Published var errorHistory: [ErrorRecord] = []
    
    private let maxErrorHistory = 50
    
    func handle(_ error: Error, context: String = "") {
        let appError: AppError
        
        if let existingAppError = error as? AppError {
            appError = existingAppError
        } else {
            appError = .unknownError(error.localizedDescription)
        }
        
        // 记录错误
        let errorRecord = ErrorRecord(
            error: appError,
            context: context,
            timestamp: Date()
        )
        
        errorHistory.insert(errorRecord, at: 0)
        
        // 限制历史记录数量
        if errorHistory.count > maxErrorHistory {
            errorHistory.removeLast(errorHistory.count - maxErrorHistory)
        }
        
        // 显示错误
        currentError = appError
        showingError = true
        
        // 日志记录
        logError(errorRecord)
        
        // 根据错误严重程度决定处理方式
        switch appError.severity {
        case .critical:
            // 关键错误，可能需要重启应用
            print("CRITICAL ERROR: \(appError.localizedDescription)")
        case .high:
            // 高级错误，影响主要功能
            print("HIGH ERROR: \(appError.localizedDescription)")
        case .medium:
            // 中级错误，部分功能受影响
            print("MEDIUM ERROR: \(appError.localizedDescription)")
        case .low:
            // 低级错误，轻微影响
            print("LOW ERROR: \(appError.localizedDescription)")
        }
    }
    
    func clearCurrentError() {
        currentError = nil
        showingError = false
    }
    
    func clearErrorHistory() {
        errorHistory.removeAll()
    }
    
    private func logError(_ errorRecord: ErrorRecord) {
        let logMessage = """
        [ERROR] \(errorRecord.timestamp)
        Context: \(errorRecord.context)
        Error: \(errorRecord.error.localizedDescription)
        Severity: \(errorRecord.error.severity)
        Recovery: \(errorRecord.error.recoverySuggestion ?? "无")
        ---
        """
        
        print(logMessage)
        
        // 可以在这里添加日志文件写入逻辑
        writeToLogFile(logMessage)
    }
    
    private func writeToLogFile(_ message: String) {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        
        let logFileURL = documentsDirectory.appendingPathComponent("app_errors.log")
        
        do {
            if FileManager.default.fileExists(atPath: logFileURL.path) {
                let fileHandle = try FileHandle(forWritingTo: logFileURL)
                fileHandle.seekToEndOfFile()
                fileHandle.write(message.data(using: .utf8) ?? Data())
                fileHandle.closeFile()
            } else {
                try message.write(to: logFileURL, atomically: true, encoding: .utf8)
            }
        } catch {
            print("Failed to write to log file: \(error)")
        }
    }
    
    // 获取错误统计
    func getErrorStatistics() -> ErrorStatistics {
        let totalErrors = errorHistory.count
        let criticalErrors = errorHistory.filter { $0.error.severity == .critical }.count
        let highErrors = errorHistory.filter { $0.error.severity == .high }.count
        let mediumErrors = errorHistory.filter { $0.error.severity == .medium }.count
        let lowErrors = errorHistory.filter { $0.error.severity == .low }.count
        
        return ErrorStatistics(
            total: totalErrors,
            critical: criticalErrors,
            high: highErrors,
            medium: mediumErrors,
            low: lowErrors
        )
    }
}

// 错误记录
struct ErrorRecord: Identifiable {
    let id = UUID()
    let error: AppError
    let context: String
    let timestamp: Date
    
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .medium
        return formatter.string(from: timestamp)
    }
}

// 错误统计
struct ErrorStatistics {
    let total: Int
    let critical: Int
    let high: Int
    let medium: Int
    let low: Int
}

// 错误显示视图
struct ErrorView: View {
    let error: AppError
    let onDismiss: () -> Void
    let onRetry: (() -> Void)?
    
    init(error: AppError, onDismiss: @escaping () -> Void, onRetry: (() -> Void)? = nil) {
        self.error = error
        self.onDismiss = onDismiss
        self.onRetry = onRetry
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // 错误图标
            Image(systemName: error.severity.icon)
                .font(.system(size: 48))
                .foregroundColor(error.severity.color)
            
            // 错误标题
            Text("发生错误")
                .font(.title2)
                .fontWeight(.semibold)
            
            // 错误描述
            Text(error.localizedDescription)
                .font(.body)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // 恢复建议
            if let suggestion = error.recoverySuggestion {
                Text(suggestion)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            // 操作按钮
            HStack(spacing: 12) {
                if let onRetry = onRetry {
                    Button("重试") {
                        onRetry()
                        onDismiss()
                    }
                    .buttonStyle(.borderedProminent)
                }
                
                Button("确定") {
                    onDismiss()
                }
                .buttonStyle(.bordered)
            }
        }
        .padding()
        .background(Color(.windowBackgroundColor))
        .cornerRadius(12)
        .shadow(radius: 10)
        .frame(maxWidth: 400)
    }
}
