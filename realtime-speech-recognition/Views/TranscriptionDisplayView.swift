//
//  TranscriptionDisplayView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

struct TranscriptionDisplayView: View {
    @ObservedObject var speechManager: SpeechRecognitionManager
    @StateObject private var textManager = TextManager()
    @State private var scrollProxy: ScrollViewReader?
    @State private var showingSearchBar = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题栏和工具栏
            HStack {
                Text("转录结果")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                // 搜索按钮
                Button(action: { showingSearchBar.toggle() }) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.blue)
                }
                .buttonStyle(PlainButtonStyle())

                // 统计信息
                if textManager.totalWords > 0 {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("\(textManager.totalWords) 词")
                            .font(.caption)
                            .foregroundColor(.blue)

                        if textManager.averageConfidence > 0 {
                            Text("置信度 \(Int(textManager.averageConfidence * 100))%")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
            }

            // 搜索栏
            if showingSearchBar {
                HStack {
                    TextField("搜索文本...", text: $textManager.searchText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .onSubmit {
                            textManager.searchWords(textManager.searchText)
                        }

                    Button("清除") {
                        textManager.clearSearch()
                    }
                    .buttonStyle(.bordered)
                }
                .transition(.opacity)
            }
            
            // 转录内容区域
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 8) {
                        // 显示所有转录段落
                        ForEach(textManager.segments) { segment in
                            TranscriptionSegmentView(
                                segment: segment,
                                showTimestamps: textManager.showTimestamps,
                                showConfidence: textManager.showConfidence,
                                highlightedWords: textManager.highlightedWords
                            )
                            .id(segment.id)
                        }

                        // 当前正在识别的文本
                        if let currentSegment = textManager.currentSegment {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    if textManager.showTimestamps {
                                        Text(currentSegment.formattedTime)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }

                                    Text(currentSegment.text)
                                        .font(.body)
                                        .foregroundColor(.blue)
                                }
                                .padding()
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(12)
                                .id("current-segment")

                                // 识别中的动画指示器
                                if speechManager.isTranscribing {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                }

                                Spacer()
                            }
                        }

                        // 空状态提示
                        if textManager.segments.isEmpty && textManager.currentSegment == nil {
                            VStack(spacing: 16) {
                                Image(systemName: "waveform")
                                    .font(.system(size: 48))
                                    .foregroundColor(.gray.opacity(0.5))

                                Text("开始录制以查看转录结果")
                                    .font(.body)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)

                                VStack(spacing: 4) {
                                    Text("• 支持实时英文语音识别")
                                    Text("• 自动检测语音活动")
                                    Text("• 词级时间戳记录")
                                }
                                .font(.caption)
                                .foregroundColor(.secondary)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 40)
                        }
                    }
                    .padding(.horizontal, 4)
                }
                .onAppear {
                    scrollProxy = proxy
                }
                .onChange(of: textManager.currentSegment) { _, _ in
                    // 自动滚动到最新内容
                    withAnimation(.easeInOut(duration: 0.3)) {
                        proxy.scrollTo("current-segment", anchor: .bottom)
                    }
                }
                .onChange(of: textManager.segments.count) { _, _ in
                    // 自动滚动到最新段落
                    if let lastSegment = textManager.segments.last {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            proxy.scrollTo(lastSegment.id, anchor: .bottom)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.02))
        .cornerRadius(16)
        .onReceive(speechManager.$transcribedText) { newText in
            // 当有新的转录文本时，创建新段落
            if !newText.isEmpty && newText != textManager.fullText {
                let segment = TranscriptionSegment(
                    text: newText,
                    startTime: Date().timeIntervalSince1970,
                    endTime: Date().timeIntervalSince1970,
                    confidence: 0.8,
                    words: [],
                    timestamp: Date()
                )
                textManager.addSegment(segment)
            }
        }
        .onReceive(speechManager.$currentSegment) { currentText in
            // 更新当前段落
            if !currentText.isEmpty {
                textManager.updateCurrentSegment(text: currentText)
            } else {
                textManager.finalizeCurrentSegment()
            }
        }
        .animation(.easeInOut(duration: 0.3), value: showingSearchBar)
    }
}

// 转录段落视图
struct TranscriptionSegmentView: View {
    let segment: TranscriptionSegment
    let showTimestamps: Bool
    let showConfidence: Bool
    let highlightedWords: Set<UUID>

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            // 时间戳
            if showTimestamps {
                Text(segment.formattedTime)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 文本内容
            if segment.words.isEmpty {
                // 简单文本显示
                Text(segment.text)
                    .font(.body)
                    .textSelection(.enabled)
            } else {
                // 词级显示
                FlowLayout(spacing: 4) {
                    ForEach(segment.words) { word in
                        Text(word.word)
                            .font(.body)
                            .padding(.horizontal, 2)
                            .background(
                                highlightedWords.contains(word.id) ?
                                Color.yellow.opacity(0.3) : Color.clear
                            )
                            .foregroundColor(
                                showConfidence ? confidenceColor(word.confidence) : .primary
                            )
                            .cornerRadius(2)
                    }
                }
            }

            // 置信度指示器
            if showConfidence {
                HStack {
                    Text("置信度: \(Int(segment.confidence * 100))%")
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("时长: \(String(format: "%.1f", segment.duration))s")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }

    private func confidenceColor(_ confidence: Float) -> Color {
        switch confidence {
        case 0.8...1.0:
            return .primary
        case 0.6..<0.8:
            return .orange
        default:
            return .red
        }
    }
}

// 简单的流式布局
struct FlowLayout: Layout {
    let spacing: CGFloat

    init(spacing: CGFloat = 8) {
        self.spacing = spacing
    }

    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        return layout(sizes: sizes, proposal: proposal).size
    }

    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        let offsets = layout(sizes: sizes, proposal: proposal).offsets

        for (offset, subview) in zip(offsets, subviews) {
            subview.place(at: CGPoint(x: bounds.minX + offset.x, y: bounds.minY + offset.y), proposal: .unspecified)
        }
    }

    private func layout(sizes: [CGSize], proposal: ProposedViewSize) -> (offsets: [CGPoint], size: CGSize) {
        let maxWidth = proposal.width ?? 300
        var offsets: [CGPoint] = []
        var currentPosition = CGPoint.zero
        var maxY: CGFloat = 0

        for size in sizes {
            if currentPosition.x + size.width > maxWidth && currentPosition.x > 0 {
                currentPosition.x = 0
                currentPosition.y = maxY + spacing
            }

            offsets.append(currentPosition)
            currentPosition.x += size.width + spacing
            maxY = max(maxY, currentPosition.y + size.height)
        }

        return (offsets, CGSize(width: maxWidth, height: maxY))
    }
}

#Preview {
    TranscriptionDisplayView(speechManager: SpeechRecognitionManager())
        .frame(height: 300)
}
