//
//  RecordingControlsView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

struct RecordingControlsView: View {
    @ObservedObject var audioManager: AudioManager
    @ObservedObject var speechManager: SpeechRecognitionManager

    let onToggleRecording: () -> Void
    let onClearTranscription: () -> Void
    let onExportText: () -> Void
    let onShowHistory: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            // 主录制按钮
            Button(action: onToggleRecording) {
                ZStack {
                    Circle()
                        .fill(audioManager.isRecording ? Color.red : Color.blue)
                        .frame(width: 80, height: 80)
                        .shadow(color: audioManager.isRecording ? .red.opacity(0.3) : .blue.opacity(0.3), radius: 10)
                    
                    Image(systemName: audioManager.isRecording ? "stop.fill" : "mic.fill")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
                .scaleEffect(audioManager.isRecording ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 0.2), value: audioManager.isRecording)
            }
            .buttonStyle(PlainButtonStyle())
            
            // 录制状态文本
            Text(audioManager.isRecording ? "点击停止录制" : "点击开始录制")
                .font(.headline)
                .foregroundColor(audioManager.isRecording ? .red : .primary)
                .animation(.easeInOut(duration: 0.2), value: audioManager.isRecording)
            
            // 控制按钮组
            HStack(spacing: 20) {
                // 清空按钮
                Button(action: onClearTranscription) {
                    VStack(spacing: 4) {
                        Image(systemName: "trash")
                            .font(.title2)
                        Text("清空")
                            .font(.caption)
                    }
                    .frame(width: 60, height: 60)
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.primary)
                    .cornerRadius(12)
                }
                .disabled(speechManager.transcribedText.isEmpty)
                .opacity(speechManager.transcribedText.isEmpty ? 0.5 : 1.0)
                
                // 导出按钮
                Button(action: onExportText) {
                    VStack(spacing: 4) {
                        Image(systemName: "square.and.arrow.up")
                            .font(.title2)
                        Text("导出")
                            .font(.caption)
                    }
                    .frame(width: 60, height: 60)
                    .background(Color.blue.opacity(0.2))
                    .foregroundColor(.blue)
                    .cornerRadius(12)
                }
                .disabled(speechManager.transcribedText.isEmpty)
                .opacity(speechManager.transcribedText.isEmpty ? 0.5 : 1.0)
                
                // 历史记录按钮
                Button(action: onShowHistory) {
                    VStack(spacing: 4) {
                        Image(systemName: "clock.arrow.circlepath")
                            .font(.title2)
                        Text("历史")
                            .font(.caption)
                    }
                    .frame(width: 60, height: 60)
                    .background(Color.purple.opacity(0.2))
                    .foregroundColor(.purple)
                    .cornerRadius(12)
                }
            }
        }
        .padding()
    }
}

#Preview {
    RecordingControlsView(
        audioManager: AudioManager(),
        speechManager: SpeechRecognitionManager(),
        onToggleRecording: {},
        onClearTranscription: {},
        onExportText: {},
        onShowHistory: {}
    )
}
