//
//  HistoryView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

struct HistoryView: View {
    @ObservedObject var dataManager: DataManager
    @State private var searchText = ""
    @State private var selectedSession: TranscriptionSession?
    @State private var showingExportSheet = false
    @State private var exportFormat: ExportFormat = .plainText
    @State private var showingDeleteAlert = false
    @State private var sessionToDelete: TranscriptionSession?
    
    var filteredSessions: [TranscriptionSession] {
        dataManager.searchSessions(query: searchText)
    }
    
    var body: some View {
        NavigationSplitView {
            // 左侧：会话列表
            VStack(spacing: 0) {
                // 搜索栏
                HStack {
                    TextField("搜索转录记录...", text: $searchText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .opacity(searchText.isEmpty ? 0 : 1)
                }
                .padding()
                
                // 统计信息
                if !dataManager.sessions.isEmpty {
                    VStack(spacing: 4) {
                        HStack {
                            Text("总计: \(dataManager.totalSessions) 个会话")
                            Spacer()
                            Text("\(dataManager.totalWords) 词")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                        
                        HStack {
                            Text("总时长: \(dataManager.formattedTotalDuration)")
                            Spacer()
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 8)
                }
                
                Divider()
                
                // 会话列表
                List(filteredSessions, selection: $selectedSession) { session in
                    SessionRowView(session: session)
                        .contextMenu {
                            Button("导出") {
                                selectedSession = session
                                showingExportSheet = true
                            }
                            
                            Button("删除", role: .destructive) {
                                sessionToDelete = session
                                showingDeleteAlert = true
                            }
                        }
                }
                .listStyle(SidebarListStyle())
                
                // 底部工具栏
                HStack {
                    Button("清空全部") {
                        showingDeleteAlert = true
                    }
                    .foregroundColor(.red)
                    .disabled(dataManager.sessions.isEmpty)
                    
                    Spacer()
                    
                    Button("导出全部") {
                        showingExportSheet = true
                    }
                    .disabled(dataManager.sessions.isEmpty)
                }
                .padding()
            }
            .navigationTitle("转录历史")
            .frame(minWidth: 300)
            
        } detail: {
            // 右侧：会话详情
            if let session = selectedSession {
                SessionDetailView(session: session)
            } else {
                VStack(spacing: 20) {
                    Image(systemName: "doc.text")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("选择一个转录会话查看详情")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    if dataManager.sessions.isEmpty {
                        Text("还没有转录记录")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .alert("确认删除", isPresented: $showingDeleteAlert) {
            if let session = sessionToDelete {
                Button("删除", role: .destructive) {
                    dataManager.deleteSession(session)
                    if selectedSession?.id == session.id {
                        selectedSession = nil
                    }
                    sessionToDelete = nil
                }
                Button("取消", role: .cancel) {
                    sessionToDelete = nil
                }
            } else {
                Button("清空全部", role: .destructive) {
                    dataManager.deleteAllSessions()
                    selectedSession = nil
                }
                Button("取消", role: .cancel) {}
            }
        } message: {
            if sessionToDelete != nil {
                Text("确定要删除这个转录会话吗？此操作无法撤销。")
            } else {
                Text("确定要清空所有转录记录吗？此操作无法撤销。")
            }
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportSessionView(
                session: selectedSession,
                dataManager: dataManager,
                format: $exportFormat
            )
        }
    }
}

// 会话行视图
struct SessionRowView: View {
    let session: TranscriptionSession
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(session.title)
                .font(.headline)
                .lineLimit(1)
            
            Text(session.formattedDate)
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack {
                Label("\(session.totalWords)", systemImage: "textformat")
                    .font(.caption2)
                    .foregroundColor(.blue)
                
                Spacer()
                
                Label(session.formattedDuration, systemImage: "clock")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if session.averageConfidence > 0 {
                ProgressView(value: session.averageConfidence, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: confidenceColor(session.averageConfidence)))
                    .frame(height: 4)
            }
        }
        .padding(.vertical, 4)
    }
    
    private func confidenceColor(_ confidence: Float) -> Color {
        switch confidence {
        case 0.8...1.0:
            return .green
        case 0.6..<0.8:
            return .orange
        default:
            return .red
        }
    }
}

// 会话详情视图
struct SessionDetailView: View {
    let session: TranscriptionSession
    @State private var showingFullText = false
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 会话信息
                VStack(alignment: .leading, spacing: 8) {
                    Text(session.title)
                        .font(.title)
                        .fontWeight(.bold)
                    
                    HStack {
                        Label(session.formattedDate, systemImage: "calendar")
                        Spacer()
                        Label(session.formattedDuration, systemImage: "clock")
                    }
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    
                    HStack {
                        Label("\(session.totalWords) 词", systemImage: "textformat")
                        Spacer()
                        Label("\(Int(session.averageConfidence * 100))% 置信度", systemImage: "checkmark.circle")
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                // 转录内容
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("转录内容")
                            .font(.headline)
                        
                        Spacer()
                        
                        Button(showingFullText ? "显示分段" : "显示全文") {
                            showingFullText.toggle()
                        }
                        .font(.caption)
                    }
                    
                    if showingFullText {
                        Text(session.fullText)
                            .font(.body)
                            .textSelection(.enabled)
                            .padding()
                            .background(Color.gray.opacity(0.05))
                            .cornerRadius(8)
                    } else {
                        LazyVStack(alignment: .leading, spacing: 8) {
                            ForEach(session.segments) { segment in
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(segment.formattedTime)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Text(segment.text)
                                        .font(.body)
                                        .textSelection(.enabled)
                                }
                                .padding()
                                .background(Color.gray.opacity(0.05))
                                .cornerRadius(8)
                            }
                        }
                    }
                }
            }
            .padding()
        }
        .navigationTitle("会话详情")
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    HistoryView(dataManager: DataManager())
}
