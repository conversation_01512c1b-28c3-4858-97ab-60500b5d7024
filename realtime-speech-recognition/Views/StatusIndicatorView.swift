//
//  StatusIndicatorView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

struct StatusIndicatorView: View {
    @ObservedObject var audioManager: AudioManager
    @ObservedObject var speechManager: SpeechRecognitionManager
    
    var body: some View {
        VStack(spacing: 12) {
            // 音频电平指示器
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: "waveform")
                        .foregroundColor(.blue)
                    Text("音频电平")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(String(format: "%.1f%%", audioManager.audioLevel * 100))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 可视化音频电平
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 8)
                            .cornerRadius(4)
                        
                        Rectangle()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [.green, .yellow, .red]),
                                startPoint: .leading,
                                endPoint: .trailing
                            ))
                            .frame(width: geometry.size.width * CGFloat(audioManager.audioLevel), height: 8)
                            .cornerRadius(4)
                            .animation(.easeInOut(duration: 0.1), value: audioManager.audioLevel)
                    }
                }
                .frame(height: 8)
            }
            
            // 状态指示器网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                // 录制状态
                StatusCard(
                    icon: "mic.circle.fill",
                    title: "录制状态",
                    value: audioManager.isRecording ? "录制中" : "未录制",
                    color: audioManager.isRecording ? .red : .gray
                )
                
                // 语音活动
                StatusCard(
                    icon: "waveform.path.ecg",
                    title: "语音检测",
                    value: speechManager.isSpeechActive ? "检测到" : "静音",
                    color: speechManager.isSpeechActive ? .green : .gray
                )
                
                // 转录状态
                StatusCard(
                    icon: "brain.head.profile",
                    title: "AI识别",
                    value: speechManager.isTranscribing ? "识别中" : "待机",
                    color: speechManager.isTranscribing ? .orange : .gray
                )
                
                // 模型状态
                StatusCard(
                    icon: "checkmark.circle.fill",
                    title: "模型状态",
                    value: speechManager.isModelLoaded ? "已加载" : "加载中",
                    color: speechManager.isModelLoaded ? .blue : .gray
                )
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
}

struct StatusCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 6) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.caption)
                Spacer()
                Circle()
                    .fill(color)
                    .frame(width: 8, height: 8)
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Text(value)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .padding(8)
        .background(Color.white.opacity(0.8))
        .cornerRadius(8)
    }
}

#Preview {
    StatusIndicatorView(
        audioManager: AudioManager(),
        speechManager: SpeechRecognitionManager()
    )
    .frame(width: 300)
}
