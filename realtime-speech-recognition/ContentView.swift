//
//  ContentView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var audioManager = AudioManager()
    @StateObject private var speechManager = SpeechRecognitionManager()
    @StateObject private var errorHandler = ErrorHandler()
    @StateObject private var performanceMonitor = PerformanceMonitor()
    @EnvironmentObject private var dataManager: DataManager
    @State private var showingExportSheet = false
    @State private var showingPerformanceMonitor = false

    var body: some View {
        Group {
            if !audioManager.hasPermission {
                PermissionView(audioManager: audioManager)
            } else if !speechManager.isModelLoaded {
                ModelLoadingView(speechManager: speechManager)
            } else {
                MainRecordingView(audioManager: audioManager, speechManager: speechManager)
            }
        }
        .onAppear {
            audioManager.checkPermissions()
            setupAudioCallback()
            performanceMonitor.startMonitoring()
        }
        .onDisappear {
            performanceMonitor.stopMonitoring()
        }
        .alert("错误", isPresented: $errorHandler.showingError) {
            if let error = errorHandler.currentError {
                Button("确定") {
                    errorHandler.clearCurrentError()
                }

                // 根据错误类型提供重试选项
                switch error {
                case .audioEngineFailure, .transcriptionFailed:
                    Button("重试") {
                        retryOperation()
                        errorHandler.clearCurrentError()
                    }
                default:
                    EmptyView()
                }
            }
        } message: {
            if let error = errorHandler.currentError {
                VStack(alignment: .leading, spacing: 4) {
                    Text(error.localizedDescription)
                    if let suggestion = error.recoverySuggestion {
                        Text(suggestion)
                            .font(.caption)
                    }
                }
            }
        }
        .onChange(of: audioManager.permissionStatus) { _, newStatus in
            if newStatus == .granted {
                audioManager.hasPermission = true
            } else if newStatus == .denied {
                errorHandler.handle(AppError.audioPermissionDenied, context: "权限检查")
            }
        }
        .onChange(of: speechManager.errorMessage) { _, errorMessage in
            if let errorMessage = errorMessage {
                errorHandler.handle(AppError.transcriptionFailed(errorMessage), context: "语音识别")
            }
        }
        .overlay(alignment: .topTrailing) {
            // 性能监控按钮
            if showingPerformanceMonitor {
                PerformanceMonitorView(monitor: performanceMonitor)
                    .frame(width: 250)
                    .transition(.opacity)
            }
        }
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                Button(action: { showingPerformanceMonitor.toggle() }) {
                    Image(systemName: "speedometer")
                }
                .help("性能监控")
            }
        }
    }

    private func setupAudioCallback() {
        audioManager.audioDataCallback = { audioData in
            speechManager.processAudioData(audioData)
        }
    }
}

// 主录制界面
struct MainRecordingView: View {
    @ObservedObject var audioManager: AudioManager
    @ObservedObject var speechManager: SpeechRecognitionManager
    @State private var showingExportSheet = false

    var body: some View {
        HSplitView {
            // 左侧：转录显示区域
            VStack(spacing: 16) {
                // 应用标题
                HStack {
                    Image(systemName: "waveform.path.ecg.rectangle")
                        .font(.title)
                        .foregroundColor(.blue)

                    VStack(alignment: .leading) {
                        Text("实时语音识别")
                            .font(.title2)
                            .fontWeight(.bold)
                        Text("Real-time Speech Recognition")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
                .padding(.horizontal)

                // 转录显示
                TranscriptionDisplayView(speechManager: speechManager)
                    .frame(minHeight: 200)

                Spacer()
            }
            .frame(minWidth: 400)

            // 右侧：控制面板
            VStack(spacing: 20) {
                // 状态指示器
                StatusIndicatorView(
                    audioManager: audioManager,
                    speechManager: speechManager
                )

                // 录制控制
                RecordingControlsView(
                    audioManager: audioManager,
                    speechManager: speechManager,
                    onToggleRecording: toggleRecording,
                    onClearTranscription: clearTranscription,
                    onExportText: exportText,
                    onShowHistory: showHistory
                )

                Spacer()
            }
            .frame(minWidth: 300, maxWidth: 350)
        }
        .alert("错误", isPresented: .constant(speechManager.errorMessage != nil)) {
            Button("确定") {
                speechManager.errorMessage = nil
            }
        } message: {
            Text(speechManager.errorMessage ?? "")
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportView(text: speechManager.exportTranscription())
        }
    }

    private func toggleRecording() {
        if audioManager.isRecording {
            // 停止录制并保存会话
            audioManager.stopRecording()
            speechManager.stopTranscription()
            performanceMonitor.recordTranscriptionEnd()

            // 保存当前会话到历史记录
            if !speechManager.transcribedText.isEmpty {
                // 这里需要从TextManager获取segments，暂时使用简化版本
                let segments = [TranscriptionSegment(
                    text: speechManager.transcribedText,
                    startTime: Date().timeIntervalSince1970 - 60, // 假设1分钟前开始
                    endTime: Date().timeIntervalSince1970,
                    confidence: 0.8,
                    words: [],
                    timestamp: Date()
                )]
                dataManager.endCurrentSession(segments: segments)
            }
        } else {
            // 开始新会话
            dataManager.startNewSession()
            speechManager.startTranscription()
            performanceMonitor.recordTranscriptionStart()

            Task {
                do {
                    try await audioManager.startRecording()
                } catch {
                    errorHandler.handle(AppError.audioEngineFailure(error.localizedDescription), context: "开始录制")
                    speechManager.stopTranscription()
                    performanceMonitor.recordTranscriptionEnd()
                }
            }
        }
    }

    private func retryOperation() {
        // 重试当前操作
        if !audioManager.isRecording {
            toggleRecording()
        }
    }

    private func clearTranscription() {
        speechManager.clearTranscription()
    }

    private func exportText() {
        showingExportSheet = true
    }

    private func showHistory() {
        // 打开历史记录窗口
        if let url = URL(string: "realtime-speech-recognition://history") {
            NSWorkspace.shared.open(url)
        }
    }
}

// 导出会话视图
struct ExportSessionView: View {
    let session: TranscriptionSession?
    let dataManager: DataManager
    @Binding var format: ExportFormat
    @Environment(\.dismiss) private var dismiss

    var exportText: String {
        if let session = session {
            return dataManager.exportSession(session, format: format)
        } else {
            return dataManager.exportAllSessions(format: format)
        }
    }

    var exportTitle: String {
        if session != nil {
            return "导出转录会话"
        } else {
            return "导出所有会话"
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text(exportTitle)
                    .font(.title2)
                    .fontWeight(.semibold)

                // 格式选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("导出格式:")
                        .font(.headline)

                    Picker("导出格式", selection: $format) {
                        ForEach(ExportFormat.allCases, id: \.self) { format in
                            Text(format.rawValue).tag(format)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }

                // 预览
                ScrollView {
                    Text(exportText)
                        .font(.body)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        .textSelection(.enabled)
                }
                .frame(maxHeight: 300)

                // 操作按钮
                HStack(spacing: 16) {
                    Button("复制到剪贴板") {
                        NSPasteboard.general.clearContents()
                        NSPasteboard.general.setString(exportText, forType: .string)
                        dismiss()
                    }
                    .buttonStyle(.borderedProminent)

                    Button("保存为文件") {
                        saveToFile()
                    }
                    .buttonStyle(.bordered)
                }
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .frame(width: 600, height: 500)
    }

    private func saveToFile() {
        let savePanel = NSSavePanel()

        switch format {
        case .json:
            savePanel.allowedContentTypes = [.json]
            savePanel.nameFieldStringValue = "转录数据_\(DateFormatter.fileNameFormatter.string(from: Date())).json"
        default:
            savePanel.allowedContentTypes = [.plainText]
            savePanel.nameFieldStringValue = "转录文本_\(DateFormatter.fileNameFormatter.string(from: Date())).txt"
        }

        if savePanel.runModal() == .OK {
            guard let url = savePanel.url else { return }

            do {
                try exportText.write(to: url, atomically: true, encoding: .utf8)
                dismiss()
            } catch {
                print("保存文件失败: \(error)")
            }
        }
    }
}

// 简化的导出视图（用于当前转录）
struct ExportView: View {
    let text: String
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("导出当前转录")
                    .font(.title2)
                    .fontWeight(.semibold)

                ScrollView {
                    Text(text)
                        .font(.body)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        .textSelection(.enabled)
                }
                .frame(maxHeight: 300)

                HStack(spacing: 16) {
                    Button("复制到剪贴板") {
                        NSPasteboard.general.clearContents()
                        NSPasteboard.general.setString(text, forType: .string)
                        dismiss()
                    }
                    .buttonStyle(.borderedProminent)

                    Button("保存为文件") {
                        saveToFile()
                    }
                    .buttonStyle(.bordered)
                }
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .frame(width: 500, height: 400)
    }

    private func saveToFile() {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.plainText]
        savePanel.nameFieldStringValue = "转录文本_\(DateFormatter.fileNameFormatter.string(from: Date())).txt"

        if savePanel.runModal() == .OK {
            guard let url = savePanel.url else { return }

            do {
                try text.write(to: url, atomically: true, encoding: .utf8)
                dismiss()
            } catch {
                print("保存文件失败: \(error)")
            }
        }
    }
}

extension DateFormatter {
    static let fileNameFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        return formatter
    }()
}

#Preview {
    ContentView()
}
