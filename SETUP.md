# macOS实时语音识别应用设置指南

## 项目概述
这是一个使用Swift和SwiftUI开发的macOS实时语音识别应用，使用WhisperKit框架实现本地语音转文字功能。

## 依赖配置

### 1. 在Xcode中添加WhisperKit依赖

1. 打开 `realtime-speech-recognition.xcodeproj`
2. 选择项目根目录
3. 在项目设置中选择 "Package Dependencies" 标签
4. 点击 "+" 按钮添加新的包依赖
5. 输入URL: `https://github.com/argmaxinc/WhisperKit.git`
6. 选择版本: "Up to Next Major" 从 "0.13.1"
7. 点击 "Add Package"
8. 在目标选择中，确保 "WhisperKit" 被添加到 "realtime-speech-recognition" 目标

### 2. 系统要求

- macOS 14.0 或更高版本
- Xcode 15.0 或更高版本
- Apple Silicon Mac（推荐，性能更佳）

### 3. 权限配置

项目已经配置了以下权限：
- 麦克风访问权限 (`NSMicrophoneUsageDescription`)
- 沙盒麦克风权限 (`com.apple.security.device.microphone`)
- 文件读写权限（用于保存转录结果）

### 4. 构建和运行

1. 确保所有依赖都已正确添加
2. 选择目标设备（Mac）
3. 按 Cmd+R 运行应用
4. 首次运行时会请求麦克风权限，请允许访问

## 项目结构

```
realtime-speech-recognition/
├── realtime_speech_recognitionApp.swift    # 应用入口
├── ContentView.swift                       # 主界面
├── Info.plist                            # 应用信息和权限
├── realtime_speech_recognition.entitlements # 沙盒权限
└── Assets.xcassets/                       # 资源文件
```

## 下一步

完成依赖配置后，可以开始实现以下功能：
1. 音频录制管理器
2. WhisperKit集成
3. 实时语音识别
4. 用户界面
5. 数据存储

## 故障排除

如果遇到构建问题：
1. 确保Xcode版本满足要求
2. 清理构建文件夹 (Product → Clean Build Folder)
3. 重新下载包依赖
4. 检查网络连接（首次下载WhisperKit模型需要网络）
