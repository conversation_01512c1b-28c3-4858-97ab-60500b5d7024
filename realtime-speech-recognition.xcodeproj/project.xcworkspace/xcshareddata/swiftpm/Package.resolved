{"originHash": "be2b508cf91e00ccfdfd83e8177b94c9aee963c0dbf9b604f03fa19a3d85c0d4", "pins": [{"identity": "jinja", "kind": "remoteSourceControl", "location": "https://github.com/johnmai-dev/<PERSON>ja", "state": {"revision": "bb238dd96fbe4c18014f3107c00edd6edb15428e", "version": "1.2.4"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser.git", "state": {"revision": "309a47b2b1d9b5e991f36961c983ecec72275be3", "version": "1.6.1"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "8c0c0a8b49e080e54e5e328cc552821ff07cd341", "version": "1.2.1"}}, {"identity": "swift-transformers", "kind": "remoteSourceControl", "location": "https://github.com/huggingface/swift-transformers.git", "state": {"revision": "8a83416cc00ab07a5de9991e6ad817a9b8588d20", "version": "0.1.15"}}, {"identity": "whisperkit", "kind": "remoteSourceControl", "location": "https://github.com/argmaxinc/WhisperKit.git", "state": {"revision": "c814caea8876036f0c87a0de29552f028d59832d", "version": "0.13.1"}}], "version": 3}